import { useState } from "react";
// import logo from "src/assets/image.webp"; // logo moet in /src/assets
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { flowConfig } from "../config/flowConfig";
import { ticketConfig } from "../config/ticketConfig";


// Typescript uitbreiding om lastAutoTable te accepteren
declare module "jspdf" {
  interface jsPDF {
    lastAutoTable: { finalY: number };
  }
}

export const EvaluationForm = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState<'phone' | 'ticket'>('phone');

  // Phone call evaluation states
  const [responses, setResponses] = useState<{ [key: string]: { score: number; note: string } }>({});
  const [agentName, setAgentName] = useState("");
  // Set date to today's date by default
  const [date] = useState(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });
  const [conversationId, setConversationId] = useState("");
  const [clientName, setClientName] = useState("Eigen Haard"); // Default client
  const [generalFeedback, setGeneralFeedback] = useState("");
  const [evaluator, setEvaluator] = useState("Tino Borst"); // Default evaluator

  // Ticket evaluation states
  const [ticketResponses, setTicketResponses] = useState<{ [key: string]: { score: number; note: string } }>({});
  const [ticketAgentName, setTicketAgentName] = useState("");
  const [ticketDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });
  const [ticketId, setTicketId] = useState("");
  const [ticketClientName, setTicketClientName] = useState("Eigen Haard"); // Default client
  const [ticketGeneralFeedback, setTicketGeneralFeedback] = useState("");
  const [ticketEvaluator, setTicketEvaluator] = useState("Tino Borst"); // Default evaluator


  const handleChange = (id: string, score: number, note: string) => {
    setResponses((prev) => ({ ...prev, [id]: { score, note } }));
  };

  const handleTicketChange = (id: string, score: number, note: string) => {
    setTicketResponses((prev) => ({ ...prev, [id]: { score, note } }));
  };

  const handleTicketExportPDF = () => {
    console.log("Ticket PDF export wordt gestart...");
    const doc = new jsPDF({ unit: "mm", format: "a4" });

    // Header background - changed to dark gray (matches form background)
    doc.setFillColor(31, 41, 55); // rgb equivalent of bg-gray-800
    doc.rect(0, 0, 210, 25, "F");

    // Logo toevoegen
    // doc.addImage(logoBase64Webp, "WEBP", 10, 4, 20, 17);

    // Header tekst - meer naar links en iets naar boven
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    doc.text("Quality Control: Ticket Evaluation", 15, 12);

    // Subheader tekst
    doc.setFontSize(12);
    doc.text("Conclusion Enablement 2025", 15, 18);

    // Details onder header
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    // Linker kolom details
    doc.text(`Datum: ${ticketDate}`, 10, 35);
    doc.text(`Agent: ${ticketAgentName}`, 10, 42);
    doc.text(`Ticket ID: ${ticketId}`, 10, 49);
    doc.text(`Klantnaam: ${ticketClientName}`, 10, 56);

    // Rechter kolom details
    doc.text(`Ticket beoordeeld door: ${ticketEvaluator}`, 120, 35);

    // Startpositie voor de eerste tabel
    let startY = 65;

    // Groepeer vragen per categorie
    const categories = Array.from(new Set(ticketConfig.map(item => item.category)));

    // Loop door elke categorie en maak een aparte tabel
    for (const category of categories) {
      // Voeg categorietitel toe
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text(category, 10, startY);

      // Filter items voor deze categorie
      const categoryItems = ticketConfig.filter(item => item.category === category);

      // Data tabel body voor deze categorie
      const tableBody = categoryItems.map(({ id, title }) => [
        title,
        ticketResponses[id]?.score?.toString() || "0",
        ticketResponses[id]?.note || "",
      ]);

      // Tabel toevoegen met autotable
      autoTable(doc, {
        startY: startY + 5,
        head: [["Vraag", "Score", "Opmerking"]],
        body: tableBody,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [31, 41, 55], textColor: 255 }, // rgb equivalent of bg-gray-800
        columnStyles: {
          0: { cellWidth: 90 },
          1: { cellWidth: 20, halign: "center" },
          2: { cellWidth: 70 },
        },
        margin: { left: 10, right: 10 },
      });

      // Update startY voor de volgende tabel (voeg wat extra ruimte toe)
      startY = doc.lastAutoTable.finalY + 15;
    }

    // Totaal score berekenen
    const ticketTotal = ticketConfig.reduce(
      (acc, q) => acc + (ticketResponses[q.id]?.score || 0),
      0
    );
    const ticketMax = ticketConfig.reduce((acc, q) => acc + q.weight, 0);
    const ticketPercentage = ((ticketTotal / ticketMax) * 100).toFixed(1);

    // Positie onder tabel
    const y = doc.lastAutoTable.finalY + 10 || 100;

    // Totaal score tekst
    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.text(`Totaalscore: ${ticketTotal} / ${ticketMax} (${ticketPercentage}%)`, 10, y);

    // Scorebalk
    const barWidth = 180;
    const filledWidth = (barWidth * ticketTotal) / ticketMax;
    const scorePercentage = (ticketTotal / ticketMax) * 100;

    // Bepaal de kleur op basis van het percentage
    if (scorePercentage <= 50) {
      // Rood voor scores tot en met 50%
      doc.setFillColor(220, 38, 38); // rgb equivalent of red-600
    } else if (scorePercentage <= 85) {
      // Oranje voor scores tot en met 85%
      doc.setFillColor(234, 88, 12); // rgb equivalent of orange-600
    } else {
      // Groen voor scores boven 85%
      doc.setFillColor(22, 163, 74); // rgb equivalent of green-600
    }

    // Teken de gevulde balk
    doc.rect(10, y + 4, filledWidth, 6, "F");

    // Teken de omtrek van de volledige balk
    doc.setDrawColor(31, 41, 55); // rgb equivalent of bg-gray-800
    doc.rect(10, y + 4, barWidth, 6);

    // Algemene feedback toevoegen
    if (ticketGeneralFeedback.trim()) {
      const feedbackY = y + 15;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("Algemene Feedback", 10, feedbackY);

      doc.setFontSize(11);
      doc.setFont("helvetica", "normal");

      // Tekst opsplitsen in regels die op de pagina passen
      const splitFeedback = doc.splitTextToSize(ticketGeneralFeedback, 180);
      doc.text(splitFeedback, 10, feedbackY + 8);
    }

    // Format date for filename
    const formattedDate = ticketDate.replace(/-/g, '');
    const filename = `${ticketAgentName}_${formattedDate}_${ticketClientName}_TICKET.pdf`.replace(/\s+/g, '_');

    doc.save(filename);
    console.log(`Ticket PDF opgeslagen als: ${filename}`);
  };

  const handleExportPDF = () => {
    console.log("PDF export wordt gestart...");
    const doc = new jsPDF({ unit: "mm", format: "a4" });

    // Header background - changed to dark gray (matches form background)
    doc.setFillColor(31, 41, 55); // rgb equivalent of bg-gray-800
    doc.rect(0, 0, 210, 25, "F");

    // Logo toevoegen
    // doc.addImage(logoBase64Webp, "WEBP", 10, 4, 20, 17);

    // Header tekst - meer naar links en iets naar boven
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    doc.text("Quality Control: SD Agent Evaluation", 15, 12);

    // Subheader tekst
    doc.setFontSize(12);
    doc.text("Conclusion Enablement 2025", 15, 18);

    // Details onder header
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    // Linker kolom details
    doc.text(`Datum: ${date}`, 10, 35);
    doc.text(`Agent: ${agentName}`, 10, 42);
    doc.text(`Gespreks ID: ${conversationId}`, 10, 49);
    doc.text(`Klantnaam: ${clientName}`, 10, 56);

    // Rechter kolom details
    doc.text(`Gesprek geluisterd door: ${evaluator}`, 120, 35);

    // Startpositie voor de eerste tabel
    let startY = 65;

    // Groepeer vragen per categorie
    const categories = Array.from(new Set(flowConfig.map(item => item.category)));

    // Loop door elke categorie en maak een aparte tabel
    for (const category of categories) {
      // Voeg categorietitel toe
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text(category, 10, startY);

      // Filter items voor deze categorie
      const categoryItems = flowConfig.filter(item => item.category === category);

      // Data tabel body voor deze categorie
      const tableBody = categoryItems.map(({ id, title }) => [
        title,
        responses[id]?.score?.toString() || "0",
        responses[id]?.note || "",
      ]);

      // Tabel toevoegen met autotable
      autoTable(doc, {
        startY: startY + 5,
        head: [["Vraag", "Score", "Opmerking"]],
        body: tableBody,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [31, 41, 55], textColor: 255 }, // rgb equivalent of bg-gray-800
        columnStyles: {
          0: { cellWidth: 90 },
          1: { cellWidth: 20, halign: "center" },
          2: { cellWidth: 70 },
        },
        margin: { left: 10, right: 10 },
      });

      // Update startY voor de volgende tabel (voeg wat extra ruimte toe)
      startY = doc.lastAutoTable.finalY + 15;
    }

    // Totaal score berekenen
    const total = flowConfig.reduce(
      (acc, q) => acc + (responses[q.id]?.score || 0),
      0
    );
    const max = flowConfig.reduce((acc, q) => acc + q.weight, 0);
    const percentage = ((total / max) * 100).toFixed(1);

    // Positie onder tabel
    const y = doc.lastAutoTable.finalY + 10 || 100;

    // Totaal score tekst
    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.text(`Totaalscore: ${total} / ${max} (${percentage}%)`, 10, y);

    // Scorebalk
    const barWidth = 180;
    const filledWidth = (barWidth * total) / max;
    const scorePercentage = (total / max) * 100;

    // Bepaal de kleur op basis van het percentage
    if (scorePercentage <= 50) {
      // Rood voor scores tot en met 50%
      doc.setFillColor(220, 38, 38); // rgb equivalent of red-600
    } else if (scorePercentage <= 85) {
      // Oranje voor scores tot en met 85%
      doc.setFillColor(234, 88, 12); // rgb equivalent of orange-600
    } else {
      // Groen voor scores boven 85%
      doc.setFillColor(22, 163, 74); // rgb equivalent of green-600
    }

    // Teken de gevulde balk
    doc.rect(10, y + 4, filledWidth, 6, "F");

    // Teken de omtrek van de volledige balk
    doc.setDrawColor(31, 41, 55); // rgb equivalent of bg-gray-800
    doc.rect(10, y + 4, barWidth, 6);

    // Algemene feedback toevoegen
    if (generalFeedback.trim()) {
      const feedbackY = y + 15;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("Algemene Feedback", 10, feedbackY);

      doc.setFontSize(11);
      doc.setFont("helvetica", "normal");

      // Tekst opsplitsen in regels die op de pagina passen
      const splitFeedback = doc.splitTextToSize(generalFeedback, 180);
      doc.text(splitFeedback, 10, feedbackY + 8);
    }

    // Format date for filename
    const formattedDate = date.replace(/-/g, '');
    const filename = `${agentName}_${formattedDate}_${clientName}.pdf`.replace(/\s+/g, '_');

    doc.save(filename);
    console.log(`PDF opgeslagen als: ${filename}`);
  };

  const total = flowConfig.reduce(
    (acc, q) => acc + (responses[q.id]?.score || 0),
    0
  );
  const max = flowConfig.reduce((acc, q) => acc + q.weight, 0);
  const percentage = ((total / max) * 100).toFixed(1);

  return (
    <div className="p-6 max-w-6xl mx-auto text-white">
      <h1 className="text-2xl font-bold mb-6">Quality Control Evaluatie</h1>

      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('phone')}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              activeTab === 'phone'
                ? 'bg-gray-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            Telefoongesprekken
          </button>
          <button
            onClick={() => setActiveTab('ticket')}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              activeTab === 'ticket'
                ? 'bg-gray-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            Tickets
          </button>
        </div>
      </div>

      {/* Phone Call Evaluation Tab */}
      {activeTab === 'phone' && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Telefoongesprek Evaluatie</h2>

          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Linker kolom */}
        <div className="space-y-4">
          <input
            type="text"
            placeholder="Naam Agent"
            value={agentName}
            onChange={(e) => setAgentName(e.target.value)}
            className="w-full p-3 rounded bg-gray-700 text-white"
          />
          <input
            type="text"
            placeholder="Gespreks ID"
            value={conversationId}
            onChange={(e) => setConversationId(e.target.value)}
            className="w-full p-3 rounded bg-gray-700 text-white"
          />
          <select
            value={clientName}
            onChange={(e) => setClientName(e.target.value)}
            className="w-full p-3 rounded bg-gray-700 text-white"
          >
            <option value="Eigen Haard">Eigen Haard</option>
            <option value="Woonin">Woonin</option>
            <option value="Woonbron">Woonbron</option>
          </select>
        </div>

        {/* Rechter kolom */}
        <div className="space-y-4">
          <input
            type="date"
            value={date}
            readOnly
            className="w-full p-3 rounded bg-gray-700 text-white"
          />
          <select
            value={evaluator}
            onChange={(e) => setEvaluator(e.target.value)}
            className="w-full p-3 rounded bg-gray-700 text-white"
          >
            <option value="Tino Borst">Tino Borst</option>
          </select>
          <div className="p-3 rounded bg-gray-700 text-white text-sm flex items-center">
            <span>Gesprek geluisterd door: <strong>{evaluator}</strong></span>
          </div>
        </div>
      </div>

      <div className="mb-6 text-lg font-semibold">
        Score: {total} / {max} (
        <span
          className={`${
            parseFloat(percentage) <= 50
              ? 'text-red-500'
              : parseFloat(percentage) <= 85
                ? 'text-orange-500'
                : 'text-green-500'
          }`}
        >
          {percentage}%
        </span>
        )
      </div>

      <form className="space-y-6">
        {/* Categorieën naast elkaar weergeven */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {/* Haal alle unieke categorieën op */}
          {Array.from(new Set(flowConfig.map(item => item.category))).map(category => (
            <div key={category} className="mb-10">
              <h2 className="text-xl font-bold mb-6 border-b border-gray-600 pb-2">{category}</h2>

              {flowConfig
                .filter(item => item.category === category)
                .map(({ id, title, description }) => (
                  <div key={id} className="bg-gray-800 p-5 rounded-lg shadow mb-5">
                    <p className="font-semibold text-lg">{title}</p>
                    <p className="text-sm text-gray-400 mb-3">{description}</p>
                    <div className="flex items-center space-x-4 mb-4">
                      {[0, 0.5, 1].map((val) => (
                        <label key={val} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name={id}
                            value={val}
                            onChange={(e) =>
                              handleChange(id, parseFloat(e.target.value), responses[id]?.note || "")
                            }
                            checked={responses[id]?.score === val}
                          />
                          <span className="text-base">{val}</span>
                        </label>
                      ))}
                    </div>
                    <textarea
                      className="w-full p-3 rounded bg-gray-700 text-white"
                      placeholder="Opmerkingen..."
                      rows={2}
                      value={responses[id]?.note || ""}
                      onChange={(e) => handleChange(id, responses[id]?.score || 0, e.target.value)}
                    />
                  </div>
                ))}
            </div>
          ))}
        </div>

        {/* Algemeen feedback veld */}
        <div className="mt-8 mb-4">
          <h2 className="text-xl font-bold mb-4 border-b border-gray-600 pb-2">Algemene Feedback</h2>
          <textarea
            className="w-full p-4 rounded bg-gray-800 text-white"
            placeholder="Voeg hier algemene feedback toe over het gesprek..."
            rows={4}
            value={generalFeedback}
            onChange={(e) => setGeneralFeedback(e.target.value)}
          />
        </div>

          <button
            type="button"
            onClick={handleExportPDF}
            className="mt-6 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded text-lg"
          >
            Exporteer Telefoongesprek naar PDF
          </button>
        </form>
        </div>
      )}

      {/* Ticket Evaluation Tab */}
      {activeTab === 'ticket' && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Ticket Evaluatie</h2>

          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Linker kolom */}
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Naam Agent"
                value={ticketAgentName}
                onChange={(e) => setTicketAgentName(e.target.value)}
                className="w-full p-3 rounded bg-gray-700 text-white"
              />
              <input
                type="text"
                placeholder="Ticket ID"
                value={ticketId}
                onChange={(e) => setTicketId(e.target.value)}
                className="w-full p-3 rounded bg-gray-700 text-white"
              />
              <select
                value={ticketClientName}
                onChange={(e) => setTicketClientName(e.target.value)}
                className="w-full p-3 rounded bg-gray-700 text-white"
              >
                <option value="Eigen Haard">Eigen Haard</option>
                <option value="Woonin">Woonin</option>
                <option value="Woonbron">Woonbron</option>
              </select>
            </div>

            {/* Rechter kolom */}
            <div className="space-y-4">
              <input
                type="date"
                value={ticketDate}
                readOnly
                className="w-full p-3 rounded bg-gray-700 text-white"
              />
              <select
                value={ticketEvaluator}
                onChange={(e) => setTicketEvaluator(e.target.value)}
                className="w-full p-3 rounded bg-gray-700 text-white"
              >
                <option value="Tino Borst">Tino Borst</option>
              </select>
              <div className="p-3 rounded bg-gray-700 text-white text-sm flex items-center">
                <span>Ticket beoordeeld door: <strong>{ticketEvaluator}</strong></span>
              </div>
            </div>
          </div>

          {/* Ticket Score Display */}
          <div className="mb-6 text-lg font-semibold">
            Score: {ticketConfig.reduce((acc, q) => acc + (ticketResponses[q.id]?.score || 0), 0)} / {ticketConfig.reduce((acc, q) => acc + q.weight, 0)} (
            <span
              className={`${
                ((ticketConfig.reduce((acc, q) => acc + (ticketResponses[q.id]?.score || 0), 0) / ticketConfig.reduce((acc, q) => acc + q.weight, 0)) * 100) <= 50
                  ? 'text-red-500'
                  : ((ticketConfig.reduce((acc, q) => acc + (ticketResponses[q.id]?.score || 0), 0) / ticketConfig.reduce((acc, q) => acc + q.weight, 0)) * 100) <= 85
                    ? 'text-orange-500'
                    : 'text-green-500'
              }`}
            >
              {(((ticketConfig.reduce((acc, q) => acc + (ticketResponses[q.id]?.score || 0), 0) / ticketConfig.reduce((acc, q) => acc + q.weight, 0)) * 100) || 0).toFixed(1)}%
            </span>
            )
          </div>

          <form className="space-y-6">
            {/* Categorieën naast elkaar weergeven */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
              {/* Haal alle unieke categorieën op */}
              {Array.from(new Set(ticketConfig.map(item => item.category))).map(category => (
                <div key={category} className="mb-10">
                  <h2 className="text-xl font-bold mb-6 border-b border-gray-600 pb-2">{category}</h2>

                  {ticketConfig
                    .filter(item => item.category === category)
                    .map(({ id, title, description }) => (
                      <div key={id} className="bg-gray-800 p-5 rounded-lg shadow mb-5">
                        <p className="font-semibold text-lg">{title}</p>
                        <p className="text-sm text-gray-400 mb-3">{description}</p>
                        <div className="flex items-center space-x-4 mb-4">
                          {[0, 0.5, 1].map((val) => (
                            <label key={val} className="flex items-center space-x-2">
                              <input
                                type="radio"
                                name={id}
                                value={val}
                                onChange={(e) =>
                                  handleTicketChange(id, parseFloat(e.target.value), ticketResponses[id]?.note || "")
                                }
                                checked={ticketResponses[id]?.score === val}
                              />
                              <span className="text-base">{val}</span>
                            </label>
                          ))}
                        </div>
                        <textarea
                          className="w-full p-3 rounded bg-gray-700 text-white"
                          placeholder="Opmerkingen..."
                          rows={2}
                          value={ticketResponses[id]?.note || ""}
                          onChange={(e) => handleTicketChange(id, ticketResponses[id]?.score || 0, e.target.value)}
                        />
                      </div>
                    ))}
                </div>
              ))}
            </div>

            {/* Algemeen feedback veld */}
            <div className="mt-8 mb-4">
              <h2 className="text-xl font-bold mb-4 border-b border-gray-600 pb-2">Algemene Feedback</h2>
              <textarea
                className="w-full p-4 rounded bg-gray-800 text-white"
                placeholder="Voeg hier algemene feedback toe over het ticket..."
                rows={4}
                value={ticketGeneralFeedback}
                onChange={(e) => setTicketGeneralFeedback(e.target.value)}
              />
            </div>

            <button
              type="button"
              onClick={handleTicketExportPDF}
              className="mt-6 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded text-lg"
            >
              Exporteer Ticket naar PDF
            </button>
          </form>
        </div>
      )}
    </div>
  );
};
