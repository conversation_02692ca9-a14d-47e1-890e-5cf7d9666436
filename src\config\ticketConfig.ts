// src/config/ticketConfig.ts
export const ticketConfig = [
  // Categorie: Ticket Intake & Verwerking
  {
    id: "klantgegevens",
    title: "Klantgegevens",
    description: "<PERSON>ijn de klantgegevens correct en volledig vastgelegd?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },
  {
    id: "prioriteitsbepaling",
    title: "Prioriteitsbepaling",
    description: "Is de juiste prioriteit toegekend aan het ticket?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },
  {
    id: "categorisering",
    title: "Categorisering",
    description: "Is het ticket correct gecategoriseerd?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },
  {
    id: "korte_omschrijving",
    title: "Korte omschrijving",
    description: "Is er een duidelijke en beknopte omschrijving van het probleem gegeven?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },
  {
    id: "omschrijving_intake",
    title: "Omschrijving / Intake",
    description: "Is de uitgebreide omschrijving en intake volledig en accuraat?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },
  {
    id: "extra_informatie",
    title: "Extra informatie logboeken of bijlagen",
    description: "Zijn relevante logboeken, screenshots of bijlagen toegevoegd?",
    weight: 1,
    category: "Ticket Intake & Verwerking"
  },

  // Categorie: Ticket Beheer & Communicatie
  {
    id: "tickets_gekoppeld",
    title: "Tickets gekoppeld",
    description: "Zijn gerelateerde tickets correct gekoppeld?",
    weight: 1,
    category: "Ticket Beheer & Communicatie"
  },
  {
    id: "strike_rule_counter",
    title: "3 strike rule counter",
    description: "Is de 3 strike rule correct toegepast en gedocumenteerd?",
    weight: 1,
    category: "Ticket Beheer & Communicatie"
  },
  {
    id: "status_updates_communicatie",
    title: "Status updates & Communicatie naar de klant",
    description: "Zijn statusupdates tijdig en duidelijk gecommuniceerd naar de klant?",
    weight: 1,
    category: "Ticket Beheer & Communicatie"
  },
  {
    id: "duidelijke_taal",
    title: "Duidelijke taal",
    description: "Is er duidelijke, professionele taal gebruikt in alle communicatie?",
    weight: 1,
    category: "Ticket Beheer & Communicatie"
  }
];
