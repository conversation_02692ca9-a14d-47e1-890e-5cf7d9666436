// src/config/ticketConfig.ts
export const ticketConfig = [
  // Categorie: Ticket Behandeling
  {
    id: "ticket_analyse",
    title: "Ticket Analyse",
    description: "Is het ticket correct geanalyseerd en begrepen?",
    weight: 1,
    category: "Ticket Behandeling"
  },
  {
    id: "ticket_prioriteit",
    title: "Prioriteit Bepaling",
    description: "Is de juiste prioriteit toegekend aan het ticket?",
    weight: 1,
    category: "Ticket Behandeling"
  },
  {
    id: "ticket_oplossing",
    title: "Oplossing Kwaliteit",
    description: "Is er een adequate oplossing geboden of correct doorverwezen?",
    weight: 1,
    category: "Ticket Behandeling"
  },
  {
    id: "ticket_communicatie",
    title: "Communicatie naar Klant",
    description: "Is de communicatie naar de klant duidelijk en professioneel?",
    weight: 1,
    category: "Ticket Behandeling"
  },
  {
    id: "ticket_afsluiting",
    title: "Ticket Afsluiting",
    description: "Is het ticket correct afgesloten met juiste status en documentatie?",
    weight: 1,
    category: "Ticket Behandeling"
  },
  
  // Categorie: Administratieve Verwerking
  {
    id: "ticket_documentatie",
    title: "Documentatie Kwaliteit",
    description: "Is de documentatie in het ticket volledig en duidelijk?",
    weight: 1,
    category: "Administratieve Verwerking"
  },
  {
    id: "ticket_categorisatie",
    title: "Categorisatie",
    description: "Is het ticket correct gecategoriseerd?",
    weight: 1,
    category: "Administratieve Verwerking"
  },
  {
    id: "ticket_tijdregistratie",
    title: "Tijdregistratie",
    description: "Is de bestede tijd correct geregistreerd?",
    weight: 1,
    category: "Administratieve Verwerking"
  },
  {
    id: "ticket_follow_up",
    title: "Follow-up Acties",
    description: "Zijn eventuele follow-up acties correct vastgelegd?",
    weight: 1,
    category: "Administratieve Verwerking"
  }
];
